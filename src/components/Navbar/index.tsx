import { NavLink, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { FC, memo, ReactElement, useCallback, useMemo } from "react";
import { NAVBAR_HEIGHT } from "@/constants";
import { setCSSVariable } from "@/lib/setCSSVariable";
import { useTracking } from "@/hooks/useTracking";
import { NavigationBar, TrackingId } from "@/constants/NavbarTrackingId";

interface NavbarIconProps {
  classname: string;
  icon: string | JSX.Element | ReactElement;
}

const NavbarIcon = ({ icon, classname }: NavbarIconProps) => {
  return (
    <div className={classname}>
      {typeof icon === "string" ? <img className="w-6 h-6" src={icon} loading="eager" alt="Icon" /> : icon}
    </div>
  );
};

export interface INavItem {
  key: string;
  path: string;
  label: string;
  activeIcon: string | JSX.Element | ReactElement;
  inActiveIcon: string | JSX.Element | ReactElement;
}

export interface NavbarProps {
  className?: string;
  navItems: INavItem[];
  activeClassName?: string;
  direction?: "row" | "column";
}

const Navbar: FC<NavbarProps> = ({ className, navItems, direction = "row" }) => {
  const location = useLocation();
  const trackEvent = useTracking(TrackingId.NavigationBar).trackEvent;
  const containerClasses = direction === "column" ? "flex flex-col" : "flex flex-row";

  const isPathMatching = useMemo(() => navItems.some((item) => item.path === location.pathname), [location.pathname]);

  const handleTrackNavbarItem = useCallback((tab_name: string) => {
    trackEvent(NavigationBar.ClickItem, { tab_name });
  }, []);

  const setNavbarHeight = useCallback((height: number) => {
    setCSSVariable("--installment-navbar-height", `${height}rem`);
  }, []);

  if (!isPathMatching) {
    setNavbarHeight(0);
    return null;
  }

  setNavbarHeight(NAVBAR_HEIGHT);

  return (
    <div
    className={cn("fixed bottom-0 left-1/2 -translate-x-1/2 z-[1] w-full max-w-[620px] bg-white", className)}
    style={{ height: `var(--installment-navbar-height,${NAVBAR_HEIGHT}rem)` }}>
      <div className={cn("w-full max-w-[420px] mx-auto justify-between px-4 py-2.5", containerClasses)}>
      {navItems.map((item: INavItem) => {
          return (
            <NavLink
              key={item.key}
              to={item.path}
              onClick={() => handleTrackNavbarItem(item.label)}
              className="flex flex-col items-center justify-center w-full h-full gap-1 group">
              {({ isActive }) => (
                <>
                  <NavbarIcon classname={isActive ? "hidden" : "block"} icon={item.inActiveIcon} />
                  <NavbarIcon classname={isActive ? "block" : "hidden"} icon={item.activeIcon} />
                  <span className={cn("text-dark-500 text-xs", isActive && "text-primary")}>{item.label}</span>
                </>
              )}
            </NavLink>
          );
        })}
      </div>
    </div>
  );
};

export default memo(Navbar);
